#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_case_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/07/17
@File_Desc: 调解案件视图集
"""

from django.db import transaction
from rest_framework import viewsets, status
from rest_framework.decorators import action, permission_classes
from rest_framework.response import Response
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend
import logging

from apps.mediation_management.models import MediationCase
from apps.mediation_management.serializers import (
    MediationCaseListSerializer,
    MediationCaseUpdateSerializer,
    MediationCaseMediatorUpdateSerializer,
    MediationCasePlanUpdateSerializer,
    MediationCaseSignatureUpdateSerializer,
    BatchUpdateStatusSerializer,
)
from utils.pagination import MyPageNumberPagination
from utils.ajax_result import AjaxResult
from utils.permission_helper import WechatFaceAuthPermission

# 获取日志记录器
logger = logging.getLogger(__name__)


class BaseMediationCaseViewSet(viewsets.ModelViewSet):
    """
    调解案件基础视图集类

    **概要说明**
    为调解案件管理提供统一的基础配置和通用功能，包括数据查询集、序列化器、分页设置和过滤配置。
    作为调解案件相关视图集的基类，确保所有调解案件视图具有一致的基础行为和配置标准。
    """

    queryset = MediationCase.objects.all().order_by("-created_time")
    serializer_class = MediationCaseListSerializer
    pagination_class = MyPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = [
        "debtor",
        "creditor",
        "mediator",
        "case_status",
        "asset_package",
    ]  # 支持按债务人、债权人、调解员、案件状态和资产包过滤
    search_fields = ["case_number"]  # 支持按案件号搜索


class MediationCaseViewSet(BaseMediationCaseViewSet):
    """
    调解案件管理视图集

    **概要说明**
    提供调解案件的完整生命周期管理功能，包括案件查询、更新、删除等核心操作。
    支持调解员分配、调解方案关联、电子签名管理等专业的案件处理功能，确保调解流程的规范化管理。
    注意：案件创建功能已禁用，调解案件现通过资产包更新流程自动创建。
    """

    def get_serializer_class(self):
        """
        根据操作类型动态选择序列化器

        **概要说明**
        根据当前视图的操作类型（action）动态选择最适合的序列化器类。
        为不同的操作提供专门的数据验证和序列化逻辑，确保数据处理的准确性和一致性。

        **请求参数**
        无需任何参数，方法内部通过 self.action 自动获取当前操作类型。

        **请求数据示例**
        此方法为内部逻辑方法，不直接处理HTTP请求数据。

        **响应数据结构**
        返回对应的序列化器类对象，不直接返回HTTP响应数据。
        - update/partial_update 操作：返回 MediationCaseUpdateSerializer
        - 其他操作：返回 MediationCaseListSerializer
        """
        if self.action in ["update", "partial_update"]:
            return MediationCaseUpdateSerializer
        else:
            return MediationCaseListSerializer

    def list(self, request, *args, **kwargs):
        """
        获取调解案件列表数据

        **概要说明**
        获取调解案件列表数据，支持分页查询、多条件过滤和关键词搜索功能。
        提供完整的案件信息，包括当事人信息、调解员信息、案件状态等核心数据，便于案件管理和监控。

        **请求参数**
        - debtor (整数, 可选): 债务人ID，用于过滤指定债务人的案件
        - creditor (整数, 可选): 债权人ID，用于过滤指定债权人的案件
        - mediator (整数, 可选): 调解员ID，用于过滤指定调解员负责的案件
        - case_status (字符串, 可选): 案件状态，可选值：draft、initiated、pending_confirm、in_progress、completed、closed
        - asset_package (整数, 可选): 资产包ID，用于过滤指定资产包关联的案件
        - search (字符串, 可选): 搜索关键词，按案件号进行模糊搜索
        - page (整数, 可选): 页码，从1开始，默认为1
        - page_size (整数, 可选): 每页记录数，默认20，最大100

        **请求数据示例**
        ```
        GET /mediation_management/mediation_case/?debtor=1&case_status=in_progress&page=1&page_size=20
        ```

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "count": 50,
                "next": "http://example.com/mediation_management/mediation_case/?page=3",
                "previous": "http://example.com/mediation_management/mediation_case/?page=1",
                "results": [
                    {
                        "id": 1,
                        "case_number": "GZTJ202507170001",
                        "case_status": "in_progress",
                        "case_status_cn": "进行中",
                        "debtor": 1,
                        "debtor_name": "张三",
                        "creditor": 1,
                        "creditor_name": "某银行股份有限公司",
                        "mediator": 1,
                        "mediator_name": "调解员李四",
                        "mediation_config": {
                            "dispute_amount": 100000,
                            "dispute_type": "债务纠纷",
                            "description": "案件详细描述"
                        },
                        "mediation_plan": 1,
                        "mediation_plan_name": "标准调解方案",
                        "asset_package": 1,
                        "asset_package_name": "2025年第一批资产包",
                        "asset_package_row_number": 15,
                        "initiate_date": "2025-07-17 10:30:00",
                        "close_date": null,
                        "mediation_agreement": null,
                        "mediation_agreement_name": null,
                        "electronic_signature": "/upload/signature.jpg",
                        "electronic_signature_name": "signature.jpg",
                        "notarization_status": "not_notarized",
                        "notarization_status_cn": "未公证",
                        "attachments": [
                            {
                                "id": 1,
                                "file_name": "附件1.pdf",
                                "secure_token": "550e8400-e29b-41d4-a716-446655440000",
                                "secure_download_url": "/user/files/download/550e8400-e29b-41d4-a716-446655440000/"
                            }
                        ],
                        "attachments_count": 1,
                        "file_cn": "附件1.pdf",
                        "mapped_field_names": ["debtor_name", "id_number", "amount"]
                    }
                ]
            }
        }
        ```
        """
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        """
        禁用调解案件直接创建功能

        **概要说明**
        调解案件现在通过资产包更新自动创建，不再支持直接创建。
        此接口已被禁用，返回405状态码提示用户使用正确的创建流程。

        **请求参数**
        不接受任何参数，该方法已被禁用。

        **请求数据示例**
        不接受任何请求数据，该方法已被禁用。

        **响应数据结构**
        ```json
        {
            "code": 405,
            "msg": "不支持直接创建调解案件，请通过资产包更新自动创建"
        }
        ```
        """
        return AjaxResult.fail(msg="不支持直接创建调解案件，请通过资产包更新自动创建", code=405)

    def retrieve(self, request, *args, **kwargs):
        """
        获取指定调解案件的详细信息

        **概要说明**
        获取指定调解案件的详细信息，包含案件的完整数据和关联信息。
        返回案件的所有字段信息，包括当事人、调解员、关联资产包、附件等完整数据。

        **请求参数**
        - id (整数, 必需): 案件ID，要查询的案件唯一标识

        **请求数据示例**
        ```
        GET /mediation_management/mediation_case/1/
        ```

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "id": 1,
                "case_number": "GZTJ202507170001",
                "case_status": "in_progress",
                "case_status_cn": "进行中",
                "debtor": 1,
                "debtor_name": "张三",
                "creditor": 1,
                "creditor_name": "某银行股份有限公司",
                "mediator": 1,
                "mediator_name": "调解员李四",
                "mediation_config": {
                    "dispute_amount": 100000,
                    "dispute_type": "债务纠纷",
                    "description": "借款合同纠纷，涉及本金及利息共计10万元"
                },
                "mediation_plan": 1,
                "mediation_plan_name": "标准调解方案",
                "asset_package": 1,
                "asset_package_name": "2025年第一批资产包",
                "asset_package_row_number": 15,
                "initiate_date": "2025-07-17 10:30:00",
                "close_date": null,
                "mediation_agreement": "/upload/agreement.pdf",
                "mediation_agreement_name": "agreement.pdf",
                "electronic_signature": "/upload/signature.jpg",
                "electronic_signature_name": "signature.jpg",
                "notarization_status": "not_notarized",
                "notarization_status_cn": "未公证",
                "attachments": [
                    {
                        "id": 1,
                        "file_name": "附件1.pdf",
                        "secure_token": "550e8400-e29b-41d4-a716-446655440000",
                        "secure_download_url": "/user/files/download/550e8400-e29b-41d4-a716-446655440000/"
                    },
                    {
                        "id": 2,
                        "file_name": "附件2.jpg",
                        "secure_token": "660e8400-e29b-41d4-a716-446655440001",
                        "secure_download_url": "/user/files/download/660e8400-e29b-41d4-a716-446655440001/"
                    }
                ],
                "attachments_count": 2,
                "file_cn": "附件1.pdf, 附件2.jpg",
                "mapped_field_names": ["debtor_name", "id_number", "amount"]
            }
        }
        ```
        """
        response = super().retrieve(request, *args, **kwargs)
        return AjaxResult.success(data=response.data)

    def update(self, request, *args, **kwargs):
        """
        更新指定调解案件的信息

        **概要说明**
        更新指定调解案件的信息，仅支持更新债务人信息和附件文件管理。
        此接口现在仅允许更新debtor、file和file_id三个字段，其他字段已被限制以确保数据一致性。

        **请求参数**
        - id (整数, 必需): 案件ID，要更新的案件唯一标识
        - debtor (整数, 可选): 债务人ID，更新关联的债务人信息
        - file (文件数组, 可选): 附件文件列表，追加新的附件文件，每个文件大小限制10MB
        - file_id (整数数组, 可选): 需要保留的附件ID列表，用于附件管理

        **请求数据示例**
        ```
        Content-Type: multipart/form-data

        debtor=2
        file=[新附件文件1二进制数据]
        file=[新附件文件2二进制数据]
        file_id=[1,3,5]
        ```

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "案件信息更新成功",
            "data": {
                "id": 1,
                "case_number": "GZTJ202507170001",
                "case_status": "draft",
                "case_status_cn": "待发起"
            }
        }
        ```
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic():  # 使用数据库事务确保数据一致性
            updated_instance = serializer.save()

        return AjaxResult.success(
            msg="案件信息更新成功",
            data={
                "id": updated_instance.id,
                "case_number": updated_instance.case_number,
                "case_status": updated_instance.case_status,
                "case_status_cn": updated_instance.get_case_status_display(),
            },
        )

    def partial_update(self, request, *args, **kwargs):
        """
        禁用部分更新（PATCH）方法

        **概要说明**
        禁用部分更新（PATCH）方法，强制使用PUT方法进行完整更新。
        此设计确保数据更新的完整性和一致性，避免部分字段更新可能导致的数据不一致问题。

        **请求参数**
        不接受任何参数，该方法已被禁用。

        **请求数据示例**
        不接受任何请求数据，该方法已被禁用。

        **响应数据结构**
        ```json
        {
            "code": 405,
            "msg": "不支持部分更新操作，请使用PUT方法进行完整更新或使用专门的更新接口",
            "data": null
        }
        ```
        """
        return Response(
            {"code": 405, "msg": "不支持部分更新操作，请使用PUT方法进行完整更新或使用专门的更新接口", "data": None},
            status=status.HTTP_405_METHOD_NOT_ALLOWED,
        )

    def destroy(self, request, *args, **kwargs):
        """
        删除指定的调解案件记录

        **概要说明**
        删除指定的调解案件记录，包括关联的附件文件和相关数据。
        删除操作使用数据库事务确保数据一致性，关联的文件会通过Django的文件字段自动处理。

        **请求参数**
        - id (整数, 必需): 案件ID，要删除的案件唯一标识

        **请求数据示例**
        ```
        DELETE /mediation_management/mediation_case/1/
        ```

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "调解案件 'GZTJ202507170001' 删除成功",
            "data": null
        }
        ```
        """
        instance = self.get_object()
        case_number = instance.case_number

        with transaction.atomic():  # 使用数据库事务确保删除操作的原子性
            # 删除案件记录（关联的文件会通过Django的文件字段自动处理）
            instance.delete()

        return AjaxResult.success(msg=f"调解案件 '{case_number}' 删除成功")

    @action(detail=True, methods=["put"])
    def update_mediator(self, request, pk=None):
        """
        更新调解案件的调解员分配信息

        **概要说明**
        更新调解案件的调解员分配信息，支持分配新调解员或取消调解员分配。
        此接口专门用于调解员的分配管理，确保案件能够分配给合适的调解员进行处理。

        **请求参数**
        - id (整数, 必需): 案件ID，要更新调解员的案件唯一标识
        - mediator (整数, 可选): 调解员ID，新的调解员用户ID，设为null可取消分配

        **请求数据示例**
        ```json
        {
            "mediator": 3
        }
        ```

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "调解员更新成功",
            "data": {
                "id": 1,
                "case_number": "GZTJ202507170001",
                "mediator": 3,
                "mediator_name": "李调解员"
            }
        }
        ```
        """
        mediation_case = self.get_object()
        serializer = MediationCaseMediatorUpdateSerializer(mediation_case, data=request.data)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic():
            updated_case = serializer.save()

        return AjaxResult.success(
            msg="调解员更新成功",
            data={
                "id": updated_case.id,
                "case_number": updated_case.case_number,
                "mediator": updated_case.mediator.id if updated_case.mediator else None,
                "mediator_name": updated_case.mediator.username if updated_case.mediator else None,
            },
        )

    @action(detail=False, methods=["get"])
    def status_choices(self, request):
        """
        获取调解案件状态选择项

        **概要说明**
        返回调解案件的所有状态选项，用于前端表单和筛选组件的选项展示。
        包含案件状态和协议公证状态两类选择项，数据格式便于前端下拉框使用。

        **请求参数**
        无需任何参数。

        **请求数据示例**
        ```
        GET /mediation_management/mediation_case/status_choices/
        ```

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "case_status_choices": [
                    {"value": "draft", "label": "待发起"},
                    {"value": "initiated", "label": "已发起"},
                    {"value": "pending_confirm", "label": "待确认"},
                    {"value": "in_progress", "label": "进行中"},
                    {"value": "completed", "label": "已完成"},
                    {"value": "closed", "label": "已关闭"}
                ],
                "notarization_status_choices": [
                    {"value": "not_notarized", "label": "未公证"},
                    {"value": "notarized", "label": "已公证"}
                ]
            }
        }
        ```
        """
        try:
            # 获取案件状态选择项
            case_status_choices = MediationCase.CASE_STATUS_CHOICES
            notarization_status_choices = MediationCase.NOTARIZATION_STATUS_CHOICES

            # 转换为前端需要的格式：[{"value": "field_name", "label": "display_name"}]
            case_status_data = [{"value": choice[0], "label": choice[1]} for choice in case_status_choices]
            notarization_status_data = [
                {"value": choice[0], "label": choice[1]} for choice in notarization_status_choices
            ]

            data = {
                "case_status_choices": case_status_data,
                "notarization_status_choices": notarization_status_data,
            }

            return AjaxResult.success(msg="操作成功", data=data)

        except Exception as e:
            return AjaxResult.fail(msg=f"获取状态选择项失败: {str(e)}")

    @action(detail=False, methods=["post"])
    def batch_update_status(self, request):
        """
        批量更新调解案件状态

        **概要说明**
        将多个调解案件的状态从"待发起"批量更新为"已发起"，并设置发起时间。
        此操作仅允许对状态为"待发起"的案件进行，确保状态流转的正确性。
        根据案件数量自动选择同步或异步处理模式。

        **请求参数**
        - case_ids (整数数组, 必需): 需要更新状态的案件ID列表，支持任意数量的批量操作

        **请求数据示例**
        ```json
        {
            "case_ids": [1, 2, 3, 4, 5]
        }
        ```

        ```json
        {
            "code": 200,
            "msg": "批量更新案件状态成功",
            "state": "success",
            "data": {
                "processing_mode": "sync",
                "updated_count": 5,
                "updated_cases": [
                    {
                        "id": 1,
                        "case_number": "GZTJ202507290001",
                        "case_status": "initiated",
                        "initiate_date": "2025-07-29T10:30:00.123456Z"
                    }
                ]
            }
        }
        ```
        """
        try:
            # 使用序列化器验证请求数据
            serializer = BatchUpdateStatusSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            case_ids = serializer.validated_data["case_ids"]
            case_count = len(case_ids)

            # 根据案件数量决定处理方式
            if case_count <= 100:
                # 同步处理：案件数量较少，直接处理并返回结果
                result = serializer.update_cases_status()
                result["processing_mode"] = "sync"

                return AjaxResult.success(
                    msg=f"批量更新案件状态成功，共更新 {result['updated_count']} 个案件", data=result
                )
            else:
                # 异步处理：案件数量较多，提交到后台队列处理
                from apps.mediation_management.tasks import batch_update_mediation_case_status_async

                # 提交异步任务
                task = batch_update_mediation_case_status_async.delay(case_ids)

                return AjaxResult.success(
                    msg=f"批量更新任务已提交，正在后台处理 {case_count} 个案件",
                    data={
                        "processing_mode": "async",
                        "task_id": task.id,
                        "total_cases": case_count,
                        "message": "任务已提交到后台队列，请稍后查询处理结果",
                    },
                )

        except Exception as e:
            # 记录错误日志并返回失败响应
            return AjaxResult.fail(msg=f"批量更新案件状态失败: {str(e)}")

    @action(detail=False, methods=["get"])
    def batch_task_status(self, request):
        """
        查询批量更新任务状态

        **概要说明**
        查询异步批量更新任务的执行状态和结果，用于跟踪大批量操作的进度。
        支持查询任务是否完成、执行结果、错误信息等详细状态。

        **请求参数**
        - task_id (字符串, 必需): 异步任务ID，从batch_update_status接口的异步响应中获取

        **请求数据示例**
        ```
        GET /mediation_management/mediation_case/batch_task_status/?task_id=abc123-def456-ghi789
        ```

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "任务状态查询成功",
            "state": "success",
            "data": {
                "task_id": "abc123-def456-ghi789",
                "status": "SUCCESS",
                "result": {
                    "updated_count": 150,
                    "updated_cases": [...],
                    "total_requested": 150
                },
                "error": null,
                "completed_at": "2025-07-29T10:35:00.123456Z"
            }
        }
        ```
        """
        try:
            task_id = request.query_params.get("task_id")
            if not task_id:
                return AjaxResult.fail(msg="缺少必需参数：task_id")

            # 导入Celery应用以查询任务状态
            from ops_management.celery import app
            from celery.result import AsyncResult

            # 获取任务结果
            task_result = AsyncResult(task_id, app=app)

            # 构造响应数据
            response_data = {
                "task_id": task_id,
                "status": task_result.status,
                "result": task_result.result if task_result.successful() else None,
                "error": str(task_result.result) if task_result.failed() else None,
                "completed_at": None,
            }

            # 如果任务已完成，尝试获取完成时间
            if task_result.status in ["SUCCESS", "FAILURE"]:
                # 注意：Celery默认不存储完成时间，这里可以根据需要扩展
                response_data["completed_at"] = task_result.date_done.isoformat() if task_result.date_done else None

            return AjaxResult.success(msg="任务状态查询成功", data=response_data)

        except Exception as e:
            return AjaxResult.fail(msg=f"查询任务状态失败: {str(e)}")

    @action(detail=True, methods=["put"])
    @permission_classes([WechatFaceAuthPermission])
    def confirm_status(self, request, pk=None):
        """
        确认调解案件状态（微信小程序专用）

        **概要说明**
        微信小程序端专用接口，用于将调解案件状态从"待确认"更新为"进行中"。
        该接口仅允许通过WechatFaceAuthPermission权限验证的微信用户访问，
        确保只有经过人脸核身认证的用户才能进行案件状态确认操作。

        **请求参数**
        - id (整数, 必需): 调解案件ID，通过URL路径传递

        **请求数据示例**
        ```
        PUT /mediation_management/mediation_case/123/confirm_status/
        ```

        **成功响应示例**
        ```json
        {
            "code": 200,
            "msg": "案件状态确认成功，已更新为进行中",
            "state": "success",
            "data": {
                "id": 123,
                "case_number": "GZTJ20250731ABC123",
                "case_status": "in_progress",
                "case_status_cn": "进行中"
            }
        }
        ```

        **错误响应示例**
        ```json
        {
            "code": 400,
            "msg": "案件当前状态为"已完成"，只有"待确认"状态的案件才能进行确认操作",
            "state": "fail",
            "data": null
        }
        ```

        **权限错误响应示例**
        ```json
        {
            "code": 403,
            "msg": "用户未通过人脸核身认证",
            "state": "fail",
            "data": null
        }
        ```
        """
        try:
            # 记录接口调用日志（脱敏处理）
            user_info = f"用户: {request.user.username[:8]}****" if hasattr(request.user, "username") else "未知用户"
            logger.info(f"调解案件状态确认接口被调用，{user_info}，案件ID: {pk}")

            # 获取调解案件对象
            try:
                case = MediationCase.objects.get(id=pk)
            except MediationCase.DoesNotExist:
                logger.warning(f"案件不存在，{user_info}，案件ID: {pk}")
                return AjaxResult.fail(msg="指定的调解案件不存在")

            # 检查案件状态是否为"待确认"
            if case.case_status != "pending_confirm":
                status_display = case.get_case_status_display()
                logger.warning(f"案件状态不符合确认条件，{user_info}，案件ID: {pk}，当前状态: {status_display}")
                return AjaxResult.fail(msg=f"案件当前状态为'{status_display}'，只有'待确认'状态的案件才能进行确认操作")

            # 使用数据库事务更新案件状态
            with transaction.atomic():
                # 更新案件状态为"进行中"
                updated_count = MediationCase.objects.filter(
                    id=pk, case_status="pending_confirm"  # 双重保险，确保只更新待确认状态的案件
                ).update(case_status="in_progress")

                if updated_count == 0:
                    logger.error(f"案件状态更新失败，可能状态已发生变化，{user_info}，案件ID: {pk}")
                    return AjaxResult.fail(msg="案件状态更新失败，可能案件状态已发生变化")

                # 重新获取更新后的案件信息
                updated_case = MediationCase.objects.get(id=pk)

            # 构造响应数据
            result_data = {
                "id": updated_case.id,
                "case_number": updated_case.case_number,
                "case_status": updated_case.case_status,
                "case_status_cn": updated_case.get_case_status_display(),
            }

            # 记录成功日志
            logger.info(
                f"案件状态确认成功，{user_info}，案件号: {updated_case.case_number}，状态: {updated_case.get_case_status_display()}"
            )

            return AjaxResult.success(msg="案件状态确认成功，已更新为进行中", data=result_data)

        except Exception as e:
            # 记录系统异常日志
            logger.error(f"案件状态确认接口发生系统异常，{user_info}，案件ID: {pk}，错误: {str(e)}")
            return AjaxResult.fail(msg=f"系统异常，请稍后重试: {str(e)}")



    @action(detail=True, methods=["put"])
    @permission_classes([WechatFaceAuthPermission])
    def update_mediation_plan(self, request, pk=None):
        """
        更新调解案件关联的调解方案信息（微信小程序专用）

        **概要说明**
        微信小程序端专用接口，用于更新调解案件关联的调解方案信息，支持关联新方案或取消方案关联。
        此接口用于将已审批通过的调解方案与具体案件进行关联，确保案件按照指定方案进行调解。

        **请求参数**
        - id (整数, 必需): 案件ID，要更新调解方案的案件唯一标识
        - mediation_plan (整数, 可选): 调解方案ID，新的调解方案ID，设为null可取消关联

        **请求数据示例**
        ```json
        {
            "mediation_plan": 2
        }
        ```

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "调解方案更新成功",
            "data": {
                "id": 1,
                "case_number": "GZTJ202507170001",
                "mediation_plan": 2,
                "mediation_plan_name": "标准还款方案（修订版）"
            }
        }
        ```
        """
        mediation_case = self.get_object()
        serializer = MediationCasePlanUpdateSerializer(mediation_case, data=request.data)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic():
            updated_case = serializer.save()

        return AjaxResult.success(
            msg="调解方案更新成功",
            data={
                "id": updated_case.id,
                "case_number": updated_case.case_number,
                "mediation_plan": updated_case.mediation_plan.id if updated_case.mediation_plan else None,
                "mediation_plan_name": updated_case.mediation_plan.plan_name if updated_case.mediation_plan else None,
            },
        )

    @action(detail=True, methods=["put"])
    @permission_classes([WechatFaceAuthPermission])
    def update_electronic_signature(self, request, pk=None):
        """
        更新调解案件的电子签名文件（微信小程序专用）

        **概要说明**
        微信小程序端专用接口，用于更新调解案件的电子签名文件，支持上传新的签名图片文件。
        此接口用于当事人上传电子签名，作为调解协议签署的重要凭证。
        当电子签名更新时，系统会自动记录当前日期时间作为签署日期，并将案件状态调整为"已完成"。

        **请求参数**
        - id (整数, 必需): 案件ID，要更新电子签名的案件唯一标识
        - electronic_signature (文件, 可选): 电子签名文件，新的签名图片文件（支持jpg、png格式）

        **请求数据示例**
        ```
        Content-Type: multipart/form-data

        electronic_signature=[图片文件二进制数据]
        ```

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "电子签名更新成功",
            "data": {
                "id": 1,
                "case_number": "GZTJ202507170001",
                "electronic_signature": "/upload/signature_new.jpg",
                "electronic_signature_name": "signature_new.jpg",
                "signature_date": "2025-08-01 14:30:00",
                "case_status": "completed",
                "case_status_cn": "已完成"
            }
        }
        ```
        """
        mediation_case = self.get_object()
        serializer = MediationCaseSignatureUpdateSerializer(mediation_case, data=request.data)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic():
            updated_case = serializer.save()

        return AjaxResult.success(
            msg="电子签名更新成功",
            data={
                "id": updated_case.id,
                "case_number": updated_case.case_number,
                "electronic_signature": (
                    updated_case.electronic_signature.url if updated_case.electronic_signature else None
                ),
                "electronic_signature_name": (
                    updated_case.electronic_signature.name.split("/")[-1] if updated_case.electronic_signature else None
                ),
                "signature_date": (
                    updated_case.signature_date.strftime("%Y-%m-%d %H:%M:%S") if updated_case.signature_date else None
                ),
                "case_status": updated_case.case_status,
                "case_status_cn": updated_case.get_case_status_display(),
            },
        )
